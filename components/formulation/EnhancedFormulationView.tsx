import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertCircle, Lightbulb } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { Formulation, FormulationStep, ProductMix } from '@/types/formulation';
import { StepDetailCard } from './StepDetailCard';

interface EnhancedFormulationViewProps {
  formulationData: Formulation | null;
  formulaText: string;
  selectedBrand: string;
  selectedLine: string;
}

export const EnhancedFormulationView: React.FC<EnhancedFormulationViewProps> = ({
  formulationData,
  formulaText,
  _selectedBrand,
  _selectedLine,
}) => {
  const [activeStep, setActiveStep] = useState(0);

  // Parse formula text if no structured data available
  const steps = useMemo<FormulationStep[]>(() => {
    if (formulationData && formulationData.steps) {
      return formulationData.steps;
    }

    // Fallback: parse text to create basic steps
    const textSteps: FormulationStep[] = [];
    const sections = formulaText.split(/(?=\d+\.)/);

    sections.forEach((section, index) => {
      if (section.trim()) {
        // Extract products from section
        const productMatches = section.match(/[-•]\s*(.+?):\s*(\d+(?:\.\d+)?)\s*(gr?|ml)/gi) || [];
        const mix = productMatches
          .map(match => {
            const parts = match.match(/[-•]\s*(.+?):\s*(\d+(?:\.\d+)?)\s*(gr?|ml)/i);
            if (parts) {
              return {
                productId: `product-${index}-${parts[1]}`,
                productName: parts[1].trim(),
                quantity: parseFloat(parts[2]),
                unit: parts[3] as 'gr' | 'ml',
              };
            }
            return null;
          })
          .filter(Boolean);

        // Extract processing time
        const timeMatch = section.match(/(\d+)\s*(?:minutos?|min)/i);
        const processingTime = timeMatch ? parseInt(timeMatch[1]) : undefined;

        textSteps.push({
          stepNumber: index + 1,
          stepTitle: `Paso ${index + 1}`,
          mix: mix as ProductMix[],
          instructions: section.trim(),
          processingTime,
        });
      }
    });

    return textSteps.length > 0
      ? textSteps
      : [
          {
            stepNumber: 1,
            stepTitle: 'Aplicación completa',
            instructions: formulaText,
            processingTime: 35,
          },
        ];
  }, [formulationData, formulaText]);

  const totalTime = useMemo(() => {
    if (formulationData?.totalTime) return formulationData.totalTime;

    return steps.reduce((sum, step) => sum + (step.processingTime || 0), 0) || 60;
  }, [formulationData, steps]);

  const warnings = formulationData?.warnings || [];

  return (
    <View style={styles.container}>
      {/* Summary Section */}
      {formulationData?.summary && (
        <View style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <Lightbulb size={20} color={Colors.light.primary} />
            <Text style={styles.summaryTitle}>Estrategia de Color</Text>
          </View>
          <Text style={styles.summaryText}>{formulationData.summary}</Text>
        </View>
      )}

      {/* Warnings Section */}
      {warnings.length > 0 && (
        <View style={styles.warningsCard}>
          <View style={styles.warningsHeader}>
            <AlertCircle size={20} color={Colors.light.warning} />
            <Text style={styles.warningsTitle}>Advertencias</Text>
          </View>
          {warnings.map((warning, index) => (
            <Text key={index} style={styles.warningText}>
              • {warning}
            </Text>
          ))}
        </View>
      )}

      {/* Steps Timeline */}
      <View style={styles.timeline}>
        {steps.map((step, index) => (
          <View key={index} style={styles.timelineItem}>
            {index > 0 && <View style={styles.timelineConnector} />}
            <StepDetailCard
              step={step}
              stepNumber={step.stepNumber}
              totalSteps={steps.length}
              isActive={activeStep === index}
              onToggle={() => setActiveStep(index)}
            />
          </View>
        ))}
      </View>

      {/* Total Time */}
      <View style={styles.totalTimeCard}>
        <Text style={styles.totalTimeLabel}>Tiempo total estimado</Text>
        <Text style={styles.totalTimeValue}>{totalTime} minutos</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  summaryCard: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + '20',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  warningsCard: {
    backgroundColor: Colors.light.warning + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.warning + '30',
  },
  warningsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  warningsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.warning,
  },
  warningText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginTop: 4,
  },
  timeline: {
    position: 'relative',
  },
  timelineItem: {
    position: 'relative',
  },
  timelineConnector: {
    position: 'absolute',
    left: 35,
    top: -12,
    width: 2,
    height: 12,
    backgroundColor: Colors.light.border,
  },
  totalTimeCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  totalTimeLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  totalTimeValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
  },
});

export default EnhancedFormulationView;
