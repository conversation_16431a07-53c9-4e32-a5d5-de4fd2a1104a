import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { typography, spacing } from '@/constants/theme';

export interface FormulaConfidenceBadgeProps {
  trustLevel?: 'certified' | 'proven' | 'validated' | 'experimental';
  trustIndicator?: string;
  confidence?: number;
  size?: 'small' | 'medium';
}

const BADGE_CONFIG = {
  certified: {
    backgroundColor: '#1B5E20',
    color: '#FFFFFF',
    emoji: '🏆'
  },
  proven: {
    backgroundColor: '#2E7D32',
    color: '#FFFFFF',
    emoji: '🟢'
  },
  validated: {
    backgroundColor: '#F57C00',
    color: '#FFFFFF',
    emoji: '🟡'
  },
  experimental: {
    backgroundColor: '#D32F2F',
    color: '#FFFFFF',
    emoji: '🔴'
  }
};

export const FormulaConfidenceBadge: React.FC<FormulaConfidenceBadgeProps> = ({
  trustLevel = 'experimental',
  trustIndicator,
  confidence,
  size = 'medium'
}) => {
  const config = BADGE_CONFIG[trustLevel];
  const isSmall = size === 'small';
  
  const confidencePercentage = confidence ? Math.round(confidence * 100) : 0;
  const emoji = trustIndicator || config.emoji;

  return (
    <View style={[
      styles.badge,
      {
        backgroundColor: config.backgroundColor,
        paddingHorizontal: isSmall ? 6 : 8,
        paddingVertical: isSmall ? 2 : 4,
        borderRadius: isSmall ? 4 : 6
      }
    ]}>
      <Text style={[
        styles.emoji,
        { fontSize: isSmall ? 10 : 12 }
      ]}>
        {emoji}
      </Text>
      <Text style={[
        styles.text,
        {
          color: config.color,
          fontSize: isSmall ? 10 : 12
        }
      ]}>
        {trustLevel.charAt(0).toUpperCase() + trustLevel.slice(1)}
      </Text>
      {confidence && (
        <Text style={[
          styles.confidence,
          {
            color: config.color,
            fontSize: isSmall ? 9 : 11
          }
        ]}>
          {confidencePercentage}%
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    gap: 4,
  },
  emoji: {
    lineHeight: 14,
  },
  text: {
    fontFamily: typography.fontFamily.medium,
    lineHeight: 14,
  },
  confidence: {
    fontFamily: typography.fontFamily.bold,
    lineHeight: 14,
  },
});

export default FormulaConfidenceBadge;
