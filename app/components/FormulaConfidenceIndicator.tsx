import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Shield, CheckCircle, AlertTriangle, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';

export interface FormulaConfidenceData {
  trustLevel?: 'certified' | 'proven' | 'validated' | 'experimental';
  trustIndicator?: string;
  trustDescription?: string;
  confidence?: number;
  isProven?: boolean;
  successCount?: number;
  avgRating?: number;
  totalUses?: number;
}

interface FormulaConfidenceIndicatorProps {
  data: FormulaConfidenceData;
  size?: 'small' | 'medium' | 'large';
  showDetails?: boolean;
}

const TRUST_LEVEL_CONFIG = {
  certified: {
    color: Colors.light.success,
    backgroundColor: '#E8F5E8',
    icon: Shield,
    label: 'Certificada',
    description: 'Fórmula con excelente historial de éxito'
  },
  proven: {
    color: Colors.light.success,
    backgroundColor: '#E8F5E8',
    icon: CheckCircle,
    label: 'Probada',
    description: 'Fórmula con buen historial de éxito'
  },
  validated: {
    color: Colors.light.warning,
    backgroundColor: '#FFF8E1',
    icon: AlertTriangle,
    label: 'Validada',
    description: 'Fórmula validada técnicamente'
  },
  experimental: {
    color: Colors.light.error,
    backgroundColor: '#FFEBEE',
    icon: AlertCircle,
    label: 'Experimental',
    description: 'Fórmula nueva - Requiere precaución'
  }
};

const SIZE_CONFIG = {
  small: {
    iconSize: 16,
    fontSize: 12,
    padding: 6,
    borderRadius: 4
  },
  medium: {
    iconSize: 20,
    fontSize: 14,
    padding: 8,
    borderRadius: 6
  },
  large: {
    iconSize: 24,
    fontSize: 16,
    padding: 12,
    borderRadius: 8
  }
};

export const FormulaConfidenceIndicator: React.FC<FormulaConfidenceIndicatorProps> = ({
  data,
  size = 'medium',
  showDetails = false
}) => {
  // Determine trust level from data
  const trustLevel = data.trustLevel || 'experimental';
  const config = TRUST_LEVEL_CONFIG[trustLevel];
  const sizeConfig = SIZE_CONFIG[size];
  const IconComponent = config.icon;

  // Format confidence percentage
  const confidencePercentage = data.confidence ? Math.round(data.confidence * 100) : 0;

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: config.backgroundColor,
        borderColor: config.color,
        padding: sizeConfig.padding,
        borderRadius: sizeConfig.borderRadius
      }
    ]}>
      <View style={styles.header}>
        <IconComponent 
          size={sizeConfig.iconSize} 
          color={config.color} 
          strokeWidth={2}
        />
        <Text style={[
          styles.label,
          {
            color: config.color,
            fontSize: sizeConfig.fontSize,
            fontWeight: '600'
          }
        ]}>
          {data.trustIndicator || ''} {config.label}
        </Text>
        {data.confidence && (
          <Text style={[
            styles.confidence,
            {
              color: config.color,
              fontSize: sizeConfig.fontSize - 2
            }
          ]}>
            {confidencePercentage}%
          </Text>
        )}
      </View>

      {showDetails && (
        <View style={styles.details}>
          <Text style={[
            styles.description,
            {
              fontSize: sizeConfig.fontSize - 2,
              color: Colors.light.text.secondary
            }
          ]}>
            {config.description}
          </Text>
          
          {data.isProven && data.successCount && (
            <View style={styles.stats}>
              <Text style={[
                styles.statsText,
                { fontSize: sizeConfig.fontSize - 2 }
              ]}>
                {data.successCount} usos exitosos
              </Text>
              {data.avgRating && (
                <Text style={[
                  styles.statsText,
                  { fontSize: sizeConfig.fontSize - 2 }
                ]}>
                  • {data.avgRating}/5.0 ⭐
                </Text>
              )}
              {data.totalUses && (
                <Text style={[
                  styles.statsText,
                  { fontSize: sizeConfig.fontSize - 2 }
                ]}>
                  • {data.totalUses} usos totales
                </Text>
              )}
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    marginVertical: spacing.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  label: {
    flex: 1,
    fontFamily: typography.fontFamily.medium,
  },
  confidence: {
    fontFamily: typography.fontFamily.bold,
  },
  details: {
    marginTop: spacing.xs,
    paddingTop: spacing.xs,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  description: {
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  stats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.xs,
    gap: spacing.xs,
  },
  statsText: {
    fontFamily: typography.fontFamily.regular,
    color: Colors.light.text.secondary,
  },
});

export default FormulaConfidenceIndicator;
