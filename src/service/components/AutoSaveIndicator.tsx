import React, { useEffect, useState, useRef } from 'react';
import { Text, StyleSheet, Animated, ActivityIndicator } from 'react-native';
import { Check, CloudOff, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useHaptics } from '@/hooks/useHaptics';

interface AutoSaveIndicatorProps {
  lastSaved?: Date;
  isSaving?: boolean;
  hasConflict?: boolean;
  isOffline?: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  lastSaved,
  isSaving = false,
  hasConflict = false,
  isOffline = false,
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));
  const [showSaved, setShowSaved] = useState(false);
  const mountedRef = useRef(true);
  const lastHapticTimeRef = useRef(0);
  const haptics = useHaptics();

  // Pulse animation when saving
  useEffect(() => {
    if (isSaving) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isSaving, pulseAnim]);

  // Show/hide animation when saving state changes
  useEffect(() => {
    if (isSaving || hasConflict || isOffline) {
      // Fade in when saving
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else if (showSaved) {
      // Show saved state briefly
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Hide after 2 seconds
      const timer = setTimeout(() => {
        if (mountedRef.current) {
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            if (mountedRef.current) {
              setShowSaved(false);
            }
          });
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isSaving, showSaved, hasConflict, isOffline, fadeAnim]);

  // Track when saving completes with debounced haptic feedback
  useEffect(() => {
    if (!isSaving && lastSaved && mountedRef.current && !hasConflict && !isOffline) {
      setShowSaved(true);

      // COMPLETELY DISABLED: haptic feedback to debug persistent vibration issue
      // Debounce haptic feedback to prevent rapid vibrations (minimum 2 seconds between haptics)
      // const now = Date.now();
      // if (now - lastHapticTimeRef.current > 2000) {
      //   lastHapticTimeRef.current = now;
      //   haptics.success();
      // }
    }
  }, [isSaving, lastSaved, hasConflict, isOffline, haptics]);

  // Track mounted state
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  if (!isSaving && !showSaved && !hasConflict && !isOffline) {
    return null;
  }

  const getContent = () => {
    if (hasConflict) {
      return {
        icon: <AlertCircle size={14} color={Colors.light.warning} />,
        text: 'Conflicto detectado',
        textStyle: styles.conflictText,
        containerStyle: [
          styles.conflictContainer,
          { backgroundColor: `${Colors.light.warning}10` },
        ],
      };
    }

    if (isOffline) {
      return {
        icon: <CloudOff size={14} color={Colors.light.gray} />,
        text: 'Sin conexión',
        textStyle: styles.offlineText,
        containerStyle: styles.offlineContainer,
      };
    }

    if (isSaving) {
      return {
        icon: <ActivityIndicator size="small" color={Colors.light.primary} />,
        text: 'Guardando...',
        textStyle: styles.savingText,
        containerStyle: [styles.savingContainer, { backgroundColor: `${Colors.light.primary}10` }],
      };
    }

    return {
      icon: <Check size={14} color={Colors.light.success} />,
      text: 'Guardado',
      textStyle: styles.savedText,
      containerStyle: [styles.savedContainer, { backgroundColor: `${Colors.light.success}10` }],
    };
  };

  const content = getContent();

  return (
    <Animated.View
      style={[
        styles.container,
        content.containerStyle,
        {
          opacity: fadeAnim,
          transform: [{ scale: isSaving ? pulseAnim : 1 }],
        },
      ]}
    >
      {content.icon}
      <Text style={[styles.text, content.textStyle]}>{content.text}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
    borderWidth: 1,
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
  savingContainer: {
    borderColor: Colors.light.primary,
  },
  savingText: {
    color: Colors.light.primary,
  },
  savedContainer: {
    borderColor: Colors.light.success,
  },
  savedText: {
    color: Colors.light.success,
  },
  conflictContainer: {
    borderColor: Colors.light.warning,
  },
  conflictText: {
    color: Colors.light.warning,
  },
  offlineContainer: {
    backgroundColor: Colors.light.surface,
    borderColor: Colors.light.border,
  },
  offlineText: {
    color: Colors.light.gray,
  },
});
