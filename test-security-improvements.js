/**
 * Script de pruebas para verificar las mejoras de seguridad en formulaciones
 * Prueba los siguientes escenarios:
 * 1. Salto de nivel excesivo (5 → 9) - <PERSON>be bloquearse
 * 2. Salto de nivel moderado (5 → 7) - <PERSON>be generar advertencias
 * 3. Salto de nivel seguro (5 → 6) - Debe pasar validación
 */

const SUPABASE_URL = 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY';

// Datos de prueba
const testCases = [
  {
    name: "CASO 1: Salto Excesivo (5→9) - DEBE BLOQUEARSE",
    diagnosis: {
      hairAnalysis: {
        averageLevel: 5,
        overallTone: "Casta<PERSON>",
        overallCondition: "Buena condición general"
      },
      detectedProcesses: {
        chemicalProcess: "Sin procesos químicos detectados"
      },
      riskDetection: {
        metallic: false,
        henna: false,
        overProcessed: false
      }
    },
    desiredResult: {
      hairAnalysis: {
        averageLevel: 9,
        overallTone: "Rubio claro"
      }
    },
    expectedResult: "BLOCKED",
    expectedWarnings: ["PROCESO TÉCNICAMENTE INVIABLE", "SALTO DE NIVEL EXCESIVO"]
  },
  {
    name: "CASO 2: Salto Moderado (5→7) - DEBE GENERAR ADVERTENCIAS",
    diagnosis: {
      hairAnalysis: {
        averageLevel: 5,
        overallTone: "Castaño",
        overallCondition: "Buena condición general"
      },
      detectedProcesses: {
        chemicalProcess: "Sin procesos químicos detectados"
      },
      riskDetection: {
        metallic: false,
        henna: false,
        overProcessed: false
      }
    },
    desiredResult: {
      hairAnalysis: {
        averageLevel: 7,
        overallTone: "Rubio medio"
      }
    },
    expectedResult: "WARNING",
    expectedWarnings: ["PROCESO COMPLEJO", "TEST DE MECHÓN"]
  },
  {
    name: "CASO 3: Salto Seguro (5→6) - DEBE PASAR VALIDACIÓN",
    diagnosis: {
      hairAnalysis: {
        averageLevel: 5,
        overallTone: "Castaño",
        overallCondition: "Buena condición general"
      },
      detectedProcesses: {
        chemicalProcess: "Sin procesos químicos detectados"
      },
      riskDetection: {
        metallic: false,
        henna: false,
        overProcessed: false
      }
    },
    desiredResult: {
      hairAnalysis: {
        averageLevel: 6,
        overallTone: "Castaño claro"
      }
    },
    expectedResult: "SAFE",
    expectedWarnings: ["FÓRMULA SEGURA", "PROCESO ESTÁNDAR"]
  }
];

async function testSecurityImprovements() {
  console.log('🧪 INICIANDO PRUEBAS DE SEGURIDAD...\n');

  const results = [];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📋 ${testCase.name}`);
    console.log('─'.repeat(60));

    try {
      const payload = {
        task: 'generate_formula',
        diagnosis: testCase.diagnosis,
        desiredResult: testCase.desiredResult,
        brand: 'Wella Professionals',
        line: 'Illumina Color',
        salonId: 'test-salon-id'
      };

      console.log(`📤 Enviando solicitud...`);

      const response = await fetch(`${SUPABASE_URL}/functions/v1/salonier-assistant`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Analizar resultado
      const result = analyzeTestResult(testCase, data);
      results.push(result);

      console.log(`📊 Resultado: ${result.status}`);
      console.log(`🔍 Advertencias encontradas: ${result.warningsFound}`);
      console.log(`✅ Validación: ${result.passed ? 'PASÓ' : 'FALLÓ'}`);

      if (!result.passed) {
        console.log(`❌ Razón del fallo: ${result.reason}`);
      }

    } catch (error) {
      console.log(`❌ Error en la prueba: ${error.message}`);
      results.push({
        testCase: testCase.name,
        status: 'ERROR',
        passed: false,
        reason: error.message
      });
    }
  }

  // Resumen final
  console.log('\n' + '='.repeat(80));
  console.log('📊 RESUMEN DE PRUEBAS');
  console.log('='.repeat(80));

  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  console.log(`✅ Pruebas pasadas: ${passed}/${total}`);
  console.log(`❌ Pruebas fallidas: ${total - passed}/${total}`);

  if (passed === total) {
    console.log('\n🎉 ¡TODAS LAS PRUEBAS PASARON! Las mejoras de seguridad funcionan correctamente.');
  } else {
    console.log('\n⚠️ ALGUNAS PRUEBAS FALLARON. Revisar implementación.');
  }

  return results;
}

function analyzeTestResult(testCase, data) {
  const result = {
    testCase: testCase.name,
    status: 'UNKNOWN',
    warningsFound: 0,
    passed: false,
    reason: '',
    warnings: []
  };

  try {
    // Verificar si la respuesta tiene la estructura esperada
    if (!data.success || !data.data) {
      result.reason = 'Respuesta inválida de la API';
      return result;
    }

    const formulationData = data.data.formulationData;
    const warnings = formulationData?.warnings || [];

    result.warningsFound = warnings.length;
    result.warnings = warnings;

    // Verificar si fue bloqueado
    const isBlocked = formulationData?.isBlocked ||
                     data.data.formulaText?.includes('Proceso Técnicamente Inviable') ||
                     warnings.some(w => w.includes('TÉCNICAMENTE INVIABLE'));

    if (isBlocked) {
      result.status = 'BLOCKED';
    } else if (warnings.length > 0) {
      result.status = 'WARNING';
    } else {
      result.status = 'SAFE';
    }

    // Verificar si el resultado coincide con lo esperado
    const expectedResult = testCase.expectedResult;
    const statusMatches = result.status === expectedResult;

    // Verificar advertencias esperadas
    const expectedWarnings = testCase.expectedWarnings;
    const warningsMatch = expectedWarnings.every(expectedWarning =>
      warnings.some(warning => warning.includes(expectedWarning))
    );

    result.passed = statusMatches && (expectedWarnings.length === 0 || warningsMatch);

    if (!statusMatches) {
      result.reason = `Estado esperado: ${expectedResult}, obtenido: ${result.status}`;
    } else if (!warningsMatch) {
      result.reason = `Advertencias esperadas no encontradas: ${expectedWarnings.join(', ')}`;
    }

  } catch (error) {
    result.reason = `Error analizando resultado: ${error.message}`;
  }

  return result;
}

// Ejecutar pruebas si se ejecuta directamente
if (typeof window === 'undefined') {
  testSecurityImprovements().catch(console.error);
}

module.exports = { testSecurityImprovements };